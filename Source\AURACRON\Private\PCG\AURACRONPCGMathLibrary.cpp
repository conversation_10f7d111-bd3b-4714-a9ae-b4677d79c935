// AURACRONPCGMathLibrary.cpp
// Biblioteca Matemática para PCG AURACRON - UE 5.6
// Implementação das funções matemáticas avançadas para geração procedural

#include "PCG/AURACRONPCGMathLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/StreamableManager.h"
#include "Engine/Public/TimerManager.h"
#include "Logging/StructuredLog.h"
#include "HAL/PlatformProperties.h"
#include "Engine/CollisionProfile.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"

// Logging category para UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONPCGMath, Log, All);

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE CURVAS E SPLINES
// ========================================

FAURACRONSplineCurve UAURACRONPCGMathLibrary::CreateSerpentineCurve(
    const FVector& StartPoint,
    const FVector& EndPoint,
    int32 NumControlPoints,
    float Amplitude,
    float Frequency)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "CreateSerpentineCurve iniciado: Start={0}, End={1}, NumPoints={2}, Amplitude={3}, Frequency={4}",
        StartPoint.ToString(), EndPoint.ToString(), NumControlPoints, Amplitude, Frequency);

    FAURACRONSplineCurve Curve;

    // Validações robustas
    if (NumControlPoints < 2)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumControlPoints muito baixo ({0}), ajustando para 2", NumControlPoints);
        NumControlPoints = 2;
    }

    if (NumControlPoints > 1000)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumControlPoints muito alto ({0}), limitando a 1000 para performance", NumControlPoints);
        NumControlPoints = 1000;
    }

    if (Amplitude <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "Amplitude inválida ({0}), usando valor padrão 1000.0f", Amplitude);
        Amplitude = 1000.0f;
    }

    if (Frequency <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "Frequency inválida ({0}), usando valor padrão 2.0f", Frequency);
        Frequency = 2.0f;
    }
    
    // Calcular direção principal
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    float TotalDistance = FVector::Dist(StartPoint, EndPoint);
    
    // Gerar pontos de controle
    for (int32 i = 0; i < NumControlPoints; ++i)
    {
        float T = static_cast<float>(i) / (NumControlPoints - 1);
        
        // Posição base ao longo da linha reta
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);
        
        // Adicionar oscilação serpentina
        float SineValue = FMath::Sin(T * Frequency * PI);
        FVector Offset = Perpendicular * SineValue * Amplitude;
        
        FVector ControlPoint = BasePosition + Offset;
        Curve.ControlPoints.Add(ControlPoint);
        
        // Calcular tangente
        FVector Tangent = Direction;
        if (i > 0 && i < NumControlPoints - 1)
        {
            float DerivativeT = T * Frequency * PI;
            float CosValue = FMath::Cos(DerivativeT);
            FVector TangentOffset = Perpendicular * CosValue * Amplitude * Frequency * PI / TotalDistance;
            Tangent = (Direction + TangentOffset).GetSafeNormal();
        }
        
        Curve.Tangents.Add(Tangent * 1000.0f); // Escalar tangente
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "CreateSerpentineCurve concluído: {0} pontos de controle gerados", Curve.ControlPoints.Num());
    return Curve;
}

FVector UAURACRONPCGMathLibrary::EvaluateSplineCurve(const FAURACRONSplineCurve& Curve, float T)
{
    // Validações robustas
    if (Curve.ControlPoints.Num() < 2)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "EvaluateSplineCurve: Curva inválida com {0} pontos de controle", Curve.ControlPoints.Num());
        return FVector::ZeroVector;
    }

    if (!FMath::IsFinite(T))
    {
        UE_LOGFMT(LogAURACRONPCGMath, Error, "EvaluateSplineCurve: Parâmetro T inválido ({0})", T);
        return FVector::ZeroVector;
    }
    
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Encontrar segmento
    float ScaledT = T * (Curve.ControlPoints.Num() - 1);
    int32 Index = FMath::FloorToInt(ScaledT);
    float LocalT = ScaledT - Index;
    
    if (Index >= Curve.ControlPoints.Num() - 1)
    {
        return Curve.ControlPoints.Last();
    }
    
    // Interpolação cúbica de Hermite
    const FVector& P0 = Curve.ControlPoints[Index];
    const FVector& P1 = Curve.ControlPoints[Index + 1];
    
    FVector T0 = (Index < Curve.Tangents.Num()) ? Curve.Tangents[Index] : FVector::ZeroVector;
    FVector T1 = (Index + 1 < Curve.Tangents.Num()) ? Curve.Tangents[Index + 1] : FVector::ZeroVector;
    
    float T2 = LocalT * LocalT;
    float T3 = T2 * LocalT;
    
    float H1 = 2.0f * T3 - 3.0f * T2 + 1.0f;
    float H2 = -2.0f * T3 + 3.0f * T2;
    float H3 = T3 - 2.0f * T2 + LocalT;
    float H4 = T3 - T2;
    
    return H1 * P0 + H2 * P1 + H3 * T0 + H4 * T1;
}

FVector UAURACRONPCGMathLibrary::GetSplineTangent(const FAURACRONSplineCurve& Curve, float T)
{
    if (Curve.ControlPoints.Num() < 2)
    {
        return FVector::ForwardVector;
    }
    
    // Calcular tangente usando diferença finita
    float Delta = 0.001f;
    FVector P1 = EvaluateSplineCurve(Curve, FMath::Clamp(T - Delta, 0.0f, 1.0f));
    FVector P2 = EvaluateSplineCurve(Curve, FMath::Clamp(T + Delta, 0.0f, 1.0f));
    
    return (P2 - P1).GetSafeNormal();
}

TArray<FVector> UAURACRONPCGMathLibrary::CreateOrbitalPath(
    const FVector& Center,
    float Radius,
    float Height,
    int32 NumPoints,
    float PhaseOffset)
{
    TArray<FVector> Points;
    
    for (int32 i = 0; i < NumPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumPoints + PhaseOffset;
        float X = Center.X + Radius * FMath::Cos(Angle);
        float Y = Center.Y + Radius * FMath::Sin(Angle);
        float Z = Center.Z + Height;
        
        Points.Add(FVector(X, Y, Z));
    }
    
    return Points;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE RUÍDO PROCEDURAL
// ========================================

float UAURACRONPCGMathLibrary::GeneratePerlinNoise2D(float X, float Y, const FAURACRONNoisePattern& Pattern)
{
    // Validações robustas
    if (!FMath::IsFinite(X) || !FMath::IsFinite(Y))
    {
        UE_LOGFMT(LogAURACRONPCGMath, Error, "GeneratePerlinNoise2D: Coordenadas inválidas X={0}, Y={1}", X, Y);
        return 0.0f;
    }

    if (Pattern.Frequency <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GeneratePerlinNoise2D: Frequency inválida ({0}), usando 1.0f", Pattern.Frequency);
        return PerlinNoise(X, Y, Pattern.Seed) * Pattern.Amplitude;
    }

    return PerlinNoise(X * Pattern.Frequency, Y * Pattern.Frequency, Pattern.Seed) * Pattern.Amplitude;
}

float UAURACRONPCGMathLibrary::GeneratePerlinNoise3D(float X, float Y, float Z, const FAURACRONNoisePattern& Pattern)
{
    // Implementação simplificada de ruído 3D usando múltiplas camadas 2D
    float Noise1 = PerlinNoise(X * Pattern.Frequency, Y * Pattern.Frequency, Pattern.Seed);
    float Noise2 = PerlinNoise(Y * Pattern.Frequency, Z * Pattern.Frequency, Pattern.Seed + 1000);
    float Noise3 = PerlinNoise(Z * Pattern.Frequency, X * Pattern.Frequency, Pattern.Seed + 2000);
    
    return (Noise1 + Noise2 + Noise3) / 3.0f * Pattern.Amplitude;
}

float UAURACRONPCGMathLibrary::GenerateFractalNoise(float X, float Y, const FAURACRONNoisePattern& Pattern)
{
    float Result = 0.0f;
    float Amplitude = Pattern.Amplitude;
    float Frequency = Pattern.Frequency;
    float MaxValue = 0.0f;
    
    for (int32 i = 0; i < Pattern.Octaves; ++i)
    {
        Result += PerlinNoise(X * Frequency, Y * Frequency, Pattern.Seed + i * 1000) * Amplitude;
        MaxValue += Amplitude;
        
        Amplitude *= Pattern.Persistence;
        Frequency *= Pattern.Lacunarity;
    }
    
    return Result / MaxValue * Pattern.Amplitude;
}

TArray<float> UAURACRONPCGMathLibrary::GenerateHeightMap(
    int32 Width,
    int32 Height,
    const FAURACRONNoisePattern& Pattern,
    float MinHeight,
    float MaxHeight)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateHeightMap iniciado: {0}x{1}, Min={2}, Max={3}",
        Width, Height, MinHeight, MaxHeight);

    // Validações robustas
    if (Width <= 0 || Height <= 0)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Error, "GenerateHeightMap: Dimensões inválidas {0}x{1}", Width, Height);
        return TArray<float>();
    }

    if (Width > 4096 || Height > 4096)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GenerateHeightMap: Dimensões muito grandes {0}x{1}, limitando para performance", Width, Height);
        Width = FMath::Min(Width, 4096);
        Height = FMath::Min(Height, 4096);
    }

    if (MinHeight >= MaxHeight)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GenerateHeightMap: MinHeight >= MaxHeight, ajustando valores");
        MaxHeight = MinHeight + 1000.0f;
    }

    TArray<float> HeightMap;
    HeightMap.Reserve(Width * Height);
    
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            float NoiseValue = GenerateFractalNoise(static_cast<float>(X), static_cast<float>(Y), Pattern);
            
            // Normalizar para [0, 1]
            float NormalizedValue = (NoiseValue + Pattern.Amplitude) / (2.0f * Pattern.Amplitude);
            NormalizedValue = FMath::Clamp(NormalizedValue, 0.0f, 1.0f);
            
            // Mapear para o intervalo de altura desejado
            float HeightValue = FMath::Lerp(MinHeight, MaxHeight, NormalizedValue);
            HeightMap.Add(HeightValue);
        }
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateHeightMap concluído: {0} valores gerados", HeightMap.Num());
    return HeightMap;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE DISTRIBUIÇÃO ESPACIAL
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
    const FVector& Center,
    float Radius,
    float MinDistance,
    int32 MaxAttempts,
    int32 Seed)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GeneratePoissonDiscSampling iniciado: Center={0}, Radius={1}, MinDist={2}, MaxAttempts={3}",
        Center.ToString(), Radius, MinDistance, MaxAttempts);

    // Validações robustas
    if (Radius <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Error, "GeneratePoissonDiscSampling: Radius inválido ({0})", Radius);
        return TArray<FVector>();
    }

    if (MinDistance <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GeneratePoissonDiscSampling: MinDistance inválida ({0}), usando 100.0f", MinDistance);
        MinDistance = 100.0f;
    }

    if (MaxAttempts <= 0)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GeneratePoissonDiscSampling: MaxAttempts inválido ({0}), usando 30", MaxAttempts);
        MaxAttempts = 30;
    }

    TArray<FVector> Points;
    TArray<FVector> ActiveList;

    FRandomStream RandomStream(Seed);
    
    // Adicionar primeiro ponto
    FVector FirstPoint = Center + FVector(
        RandomStream.FRandRange(-Radius, Radius),
        RandomStream.FRandRange(-Radius, Radius),
        0.0f
    );
    
    Points.Add(FirstPoint);
    ActiveList.Add(FirstPoint);
    
    while (ActiveList.Num() > 0)
    {
        int32 RandomIndex = RandomStream.RandRange(0, ActiveList.Num() - 1);
        FVector CurrentPoint = ActiveList[RandomIndex];
        bool bFoundValidPoint = false;
        
        for (int32 Attempt = 0; Attempt < MaxAttempts; ++Attempt)
        {
            float Angle = RandomStream.FRandRange(0.0f, 2.0f * PI);
            float Distance = RandomStream.FRandRange(MinDistance, 2.0f * MinDistance);
            
            FVector NewPoint = CurrentPoint + FVector(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                CurrentPoint.Z
            );
            
            // Verificar se está dentro do raio
            if (FVector::Dist2D(NewPoint, Center) > Radius)
            {
                continue;
            }
            
            // Verificar distância mínima de outros pontos
            bool bTooClose = false;
            for (const FVector& ExistingPoint : Points)
            {
                if (FVector::Dist2D(NewPoint, ExistingPoint) < MinDistance)
                {
                    bTooClose = true;
                    break;
                }
            }
            
            if (!bTooClose)
            {
                Points.Add(NewPoint);
                ActiveList.Add(NewPoint);
                bFoundValidPoint = true;
                break;
            }
        }
        
        if (!bFoundValidPoint)
        {
            ActiveList.RemoveAt(RandomIndex);
        }
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GeneratePoissonDiscSampling concluído: {0} pontos gerados", Points.Num());
    return Points;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateHexagonalGrid(
    const FVector& Center,
    float Radius,
    float Spacing,
    bool bAddRandomOffset,
    float RandomOffsetAmount)
{
    TArray<FVector> Points;
    FRandomStream RandomStream(12345);
    
    int32 MaxRings = FMath::CeilToInt(Radius / Spacing);
    
    // Centro
    FVector CenterPoint = Center;
    if (bAddRandomOffset)
    {
        CenterPoint += FVector(
            RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
            RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
            0.0f
        );
    }
    Points.Add(CenterPoint);
    
    // Anéis hexagonais
    for (int32 Ring = 1; Ring <= MaxRings; ++Ring)
    {
        int32 PointsInRing = 6 * Ring;
        
        for (int32 i = 0; i < PointsInRing; ++i)
        {
            float Angle = 2.0f * PI * i / PointsInRing;
            float Distance = Ring * Spacing;
            
            FVector Point = Center + FVector(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                Center.Z
            );
            
            if (bAddRandomOffset)
            {
                Point += FVector(
                    RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
                    RandomStream.FRandRange(-Spacing * RandomOffsetAmount, Spacing * RandomOffsetAmount),
                    0.0f
                );
            }
            
            // Verificar se está dentro do raio
            if (FVector::Dist2D(Point, Center) <= Radius)
            {
                Points.Add(Point);
            }
        }
    }
    
    return Points;
}

TArray<FVector> UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
    const FAURACRONSplineCurve& Curve,
    float Spacing,
    bool bAlignToTangent)
{
    TArray<FVector> Points;
    
    if (Curve.ControlPoints.Num() < 2 || Spacing <= 0.0f)
    {
        return Points;
    }
    
    // Estimar comprimento da curva
    float CurveLength = 0.0f;
    const int32 LengthSamples = 100;
    FVector PreviousPoint = EvaluateSplineCurve(Curve, 0.0f);
    
    for (int32 i = 1; i <= LengthSamples; ++i)
    {
        float T = static_cast<float>(i) / LengthSamples;
        FVector CurrentPoint = EvaluateSplineCurve(Curve, T);
        CurveLength += FVector::Dist(PreviousPoint, CurrentPoint);
        PreviousPoint = CurrentPoint;
    }
    
    // Distribuir pontos
    int32 NumPoints = FMath::FloorToInt(CurveLength / Spacing);
    
    for (int32 i = 0; i <= NumPoints; ++i)
    {
        float T = static_cast<float>(i) / FMath::Max(NumPoints, 1);
        FVector Point = EvaluateSplineCurve(Curve, T);
        Points.Add(Point);
    }
    
    return Points;
}

// ========================================
// FUNÇÕES AUXILIARES INTERNAS
// ========================================

float UAURACRONPCGMathLibrary::SmoothStep(float Edge0, float Edge1, float X)
{
    float T = FMath::Clamp((X - Edge0) / (Edge1 - Edge0), 0.0f, 1.0f);
    return T * T * (3.0f - 2.0f * T);
}

float UAURACRONPCGMathLibrary::PerlinNoise(float X, float Y, int32 Seed)
{
    int32 Xi = FMath::FloorToInt(X) & 255;
    int32 Yi = FMath::FloorToInt(Y) & 255;
    
    float Xf = X - FMath::FloorToFloat(X);
    float Yf = Y - FMath::FloorToFloat(Y);
    
    float U = Fade(Xf);
    float V = Fade(Yf);
    
    int32 AA = Hash(Xi, Yi, Seed);
    int32 AB = Hash(Xi, Yi + 1, Seed);
    int32 BA = Hash(Xi + 1, Yi, Seed);
    int32 BB = Hash(Xi + 1, Yi + 1, Seed);
    
    float X1 = FMath::Lerp(Grad(AA, Xf, Yf), Grad(BA, Xf - 1, Yf), U);
    float X2 = FMath::Lerp(Grad(AB, Xf, Yf - 1), Grad(BB, Xf - 1, Yf - 1), U);
    
    return FMath::Lerp(X1, X2, V);
}

float UAURACRONPCGMathLibrary::Fade(float T)
{
    return T * T * T * (T * (T * 6.0f - 15.0f) + 10.0f);
}

float UAURACRONPCGMathLibrary::Grad(int32 Hash, float X, float Y)
{
    int32 H = Hash & 15;
    float U = H < 8 ? X : Y;
    float V = H < 4 ? Y : H == 12 || H == 14 ? X : 0;
    return ((H & 1) == 0 ? U : -U) + ((H & 2) == 0 ? V : -V);
}

int32 UAURACRONPCGMathLibrary::Hash(int32 X, int32 Y, int32 Seed)
{
    int32 Hash = Seed;
    Hash ^= X * 374761393 + Y * 668265263;
    Hash = (Hash ^ (Hash >> 13)) * 1274126177;
    return Hash ^ (Hash >> 16);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE GEOMETRIA PROCEDURAL
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GenerateCrystallinePlateauVertices(
    const FVector& Center,
    float Radius,
    float Height,
    int32 NumSides,
    float Irregularity)
{
    TArray<FVector> Vertices;
    FRandomStream RandomStream(12345);

    // Base do platô
    for (int32 i = 0; i < NumSides; ++i)
    {
        float Angle = 2.0f * PI * i / NumSides;
        float RadiusVariation = 1.0f + RandomStream.FRandRange(-Irregularity, Irregularity);
        float ActualRadius = Radius * RadiusVariation;

        float X = Center.X + ActualRadius * FMath::Cos(Angle);
        float Y = Center.Y + ActualRadius * FMath::Sin(Angle);

        Vertices.Add(FVector(X, Y, Center.Z));
    }

    // Topo do platô
    for (int32 i = 0; i < NumSides; ++i)
    {
        float Angle = 2.0f * PI * i / NumSides;
        float RadiusVariation = 1.0f + RandomStream.FRandRange(-Irregularity * 0.5f, Irregularity * 0.5f);
        float ActualRadius = Radius * 0.8f * RadiusVariation; // Topo menor que a base

        float X = Center.X + ActualRadius * FMath::Cos(Angle);
        float Y = Center.Y + ActualRadius * FMath::Sin(Angle);

        Vertices.Add(FVector(X, Y, Center.Z + Height));
    }

    return Vertices;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateLivingCanyonPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float Width,
    float Depth,
    int32 NumSegments)
{
    TArray<FVector> PathPoints;

    // Criar curva serpentina para o cânion
    FAURACRONSplineCurve CanyonCurve = CreateSerpentineCurve(
        StartPoint, EndPoint, NumSegments, Width * 0.5f, 2.0f
    );

    // Gerar pontos ao longo da curva
    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float T = static_cast<float>(i) / NumSegments;
        FVector CenterPoint = EvaluateSplineCurve(CanyonCurve, T);

        // Adicionar profundidade variável
        float DepthVariation = 1.0f + 0.3f * FMath::Sin(T * 4.0f * PI);
        CenterPoint.Z -= Depth * DepthVariation;

        PathPoints.Add(CenterPoint);

        // Adicionar pontos laterais para definir a largura
        FVector Tangent = GetSplineTangent(CanyonCurve, T);
        FVector Perpendicular = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

        PathPoints.Add(CenterPoint + Perpendicular * Width * 0.5f);
        PathPoints.Add(CenterPoint - Perpendicular * Width * 0.5f);
    }

    return PathPoints;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateBreathingForestPositions(
    const FVector& Center,
    float Radius,
    int32 TreeCount,
    float MinDistance,
    float BreathingAmplitude,
    float Time)
{
    TArray<FVector> TreePositions;

    // Gerar posições base usando amostragem de Poisson
    TArray<FVector> BasePositions = GeneratePoissonDiscSampling(
        Center, Radius, MinDistance, 30, 54321
    );

    // Limitar ao número desejado de árvores
    int32 ActualTreeCount = FMath::Min(TreeCount, BasePositions.Num());

    for (int32 i = 0; i < ActualTreeCount; ++i)
    {
        FVector BasePosition = BasePositions[i];

        // Adicionar efeito de "respiração" - movimento sutil baseado no tempo
        float BreathingPhase = Time + i * 0.1f; // Cada árvore com fase ligeiramente diferente
        float BreathingOffset = FMath::Sin(BreathingPhase) * BreathingAmplitude;

        // Movimento em direção radial do centro
        FVector DirectionFromCenter = (BasePosition - Center).GetSafeNormal2D();
        FVector BreathingPosition = BasePosition + DirectionFromCenter * BreathingOffset;

        TreePositions.Add(BreathingPosition);
    }

    return TreePositions;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateTectonicBridgePoints(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float Width,
    int32 NumSupports,
    float ArchHeight)
{
    TArray<FVector> BridgePoints;

    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    float BridgeLength = FVector::Dist(StartPoint, EndPoint);

    // Pontos principais da ponte
    for (int32 i = 0; i <= NumSupports + 1; ++i)
    {
        float T = static_cast<float>(i) / (NumSupports + 1);
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);

        // Adicionar curvatura de arco
        float ArchT = FMath::Sin(T * PI);
        BasePosition.Z += ArchHeight * ArchT;

        BridgePoints.Add(BasePosition);

        // Adicionar pontos laterais para definir a largura
        BridgePoints.Add(BasePosition + Perpendicular * Width * 0.5f);
        BridgePoints.Add(BasePosition - Perpendicular * Width * 0.5f);
    }

    return BridgePoints;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE TRANSFORMAÇÃO TEMPORAL
// ========================================

FVector UAURACRONPCGMathLibrary::GetTimeBasedPosition(
    const FVector& BasePosition,
    float TimeOfDay,
    float Amplitude,
    float Frequency)
{
    float TimeRadians = TimeOfDay * 2.0f * PI * Frequency;

    FVector Offset = FVector(
        FMath::Sin(TimeRadians) * Amplitude,
        FMath::Cos(TimeRadians) * Amplitude,
        FMath::Sin(TimeRadians * 0.5f) * Amplitude * 0.5f
    );

    return BasePosition + Offset;
}

float UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(float TimeOfDay, float LunarCycle)
{
    // Simular ciclo lunar (0 = lua nova, 0.5 = lua cheia)
    float LunarPhase = FMath::Fmod(TimeOfDay * LunarCycle, 1.0f);

    // Intensidade máxima na lua cheia
    float Intensity = 1.0f - FMath::Abs(LunarPhase - 0.5f) * 2.0f;

    // Considerar também a hora do dia (lua mais visível à noite)
    float NightIntensity = FMath::Max(0.0f, FMath::Cos((TimeOfDay - 0.5f) * 2.0f * PI));

    return Intensity * NightIntensity;
}

float UAURACRONPCGMathLibrary::GetSolarIntensity(float TimeOfDay)
{
    // TimeOfDay de 0 a 1 representa 24 horas
    // Intensidade máxima ao meio-dia (0.5)
    float SolarAngle = (TimeOfDay - 0.5f) * 2.0f * PI;
    float Intensity = FMath::Max(0.0f, FMath::Cos(SolarAngle));

    return Intensity;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE OTIMIZAÇÃO E PERFORMANCE
// ========================================

int32 UAURACRONPCGMathLibrary::CalculateLODLevel(
    const FVector& ObjectPosition,
    const FVector& ViewerPosition,
    float LOD0Distance,
    float LOD1Distance,
    float LOD2Distance)
{
    float Distance = FVector::Dist(ObjectPosition, ViewerPosition);

    if (Distance <= LOD0Distance)
    {
        return 0; // Máxima qualidade
    }
    else if (Distance <= LOD1Distance)
    {
        return 1; // Qualidade média
    }
    else if (Distance <= LOD2Distance)
    {
        return 2; // Qualidade baixa
    }
    else
    {
        return 3; // Não renderizar
    }
}

bool UAURACRONPCGMathLibrary::ShouldRenderObject(
    const FVector& ObjectPosition,
    float ObjectRadius,
    const FVector& ViewerPosition,
    const FVector& ViewerForward,
    float ViewDistance,
    float FOVDegrees)
{
    // Verificar distância
    float Distance = FVector::Dist(ObjectPosition, ViewerPosition);
    if (Distance > ViewDistance + ObjectRadius)
    {
        return false;
    }

    // Verificar frustum (simplificado)
    FVector ToObject = (ObjectPosition - ViewerPosition).GetSafeNormal();
    float DotProduct = FVector::DotProduct(ViewerForward, ToObject);
    float FOVRadians = FMath::DegreesToRadians(FOVDegrees * 0.5f);

    if (DotProduct < FMath::Cos(FOVRadians))
    {
        // Verificar se o objeto está parcialmente visível considerando seu raio
        float AngleToObject = FMath::Acos(DotProduct);
        float ObjectAngularSize = FMath::Atan(ObjectRadius / Distance);

        return (AngleToObject - ObjectAngularSize) <= FOVRadians;
    }

    return true;
}

float UAURACRONPCGMathLibrary::CalculateObjectDensity(
    float BaselineFrameRate,
    float CurrentFrameRate,
    float MinDensity,
    float MaxDensity)
{
    if (BaselineFrameRate <= 0.0f)
    {
        return MaxDensity;
    }

    float PerformanceRatio = CurrentFrameRate / BaselineFrameRate;
    PerformanceRatio = FMath::Clamp(PerformanceRatio, 0.1f, 2.0f);

    // Se o framerate está baixo, reduzir densidade
    float Density = FMath::Lerp(MinDensity, MaxDensity, PerformanceRatio);

    return FMath::Clamp(Density, MinDensity, MaxDensity);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE COR E EFEITOS VISUAIS
// ========================================

FLinearColor UAURACRONPCGMathLibrary::ShiftHue(const FLinearColor& Color, float HueShift)
{
    // Converter RGB para HSV
    FLinearColor HSV = Color.LinearRGBToHSV();

    // Ajustar matiz
    HSV.R = FMath::Fmod(HSV.R + HueShift * 360.0f, 360.0f);
    if (HSV.R < 0.0f)
    {
        HSV.R += 360.0f;
    }

    // Converter de volta para RGB
    return HSV.HSVToLinearRGB();
}

float UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(const UWorld* World)
{
    if (!World)
    {
        return 0.5f; // Valor padrão
    }

    // Usar o tempo do mundo para calcular a fase lunar
    float WorldTime = World->GetTimeSeconds();

    // Simular um ciclo lunar de aproximadamente 30 dias (em segundos do jogo)
    float LunarCycleDuration = 30.0f * 24.0f * 60.0f; // 30 dias em segundos
    float LunarPhase = FMath::Fmod(WorldTime, LunarCycleDuration) / LunarCycleDuration;

    // Calcular intensidade baseada na fase (0 = lua nova, 0.5 = lua cheia)
    float Intensity = 1.0f - FMath::Abs(LunarPhase - 0.5f) * 2.0f;

    // Considerar também a hora do dia (lua mais visível à noite)
    float TimeOfDay = FMath::Fmod(WorldTime / (24.0f * 60.0f * 60.0f), 1.0f); // Normalizar para 0-1
    float NightIntensity = FMath::Max(0.0f, FMath::Cos((TimeOfDay - 0.5f) * 2.0f * PI));

    return FMath::Clamp(Intensity * NightIntensity, 0.0f, 1.0f);
}

float UAURACRONPCGMathLibrary::GetTerrainHeightAtLocation(const UWorld* World, const FVector& Location)
{
    // Implementação robusta para obter altura do terreno usando APIs modernas do UE 5.6
    if (!World)
    {
        return 0.0f;
    }

    // Usar line trace para obter altura do terreno
    FVector TraceStart = Location + FVector(0.0f, 0.0f, 10000.0f); // Começar bem acima
    FVector TraceEnd = Location - FVector(0.0f, 0.0f, 10000.0f);   // Terminar bem abaixo

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    // Realizar line trace para encontrar o chão
    if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        return HitResult.Location.Z;
    }

    // Se não encontrou nada, retornar altura padrão
    return Location.Z;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ESPECÍFICAS DO FLUXO PRISMAL
// ========================================

FAURACRONSplineCurve UAURACRONPCGMathLibrary::GeneratePrismalFlowPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float FlowWidth,
    int32 NumSegments,
    float SerpentineAmplitude,
    float SerpentineFrequency)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GeneratePrismalFlowPath iniciado: Start={0}, End={1}, Width={2}, Segments={3}",
        StartPoint.ToString(), EndPoint.ToString(), FlowWidth, NumSegments);

    // Validações robustas
    if (NumSegments < 10)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumSegments muito baixo ({0}), ajustando para 10", NumSegments);
        NumSegments = 10;
    }

    if (FlowWidth <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "FlowWidth inválida ({0}), usando valor padrão 2000.0f", FlowWidth);
        FlowWidth = 2000.0f;
    }

    // Criar curva serpentina para o Fluxo Prismal baseada na documentação
    FAURACRONSplineCurve PrismalCurve = CreateSerpentineCurve(
        StartPoint, EndPoint, NumSegments, SerpentineAmplitude, SerpentineFrequency
    );

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GeneratePrismalFlowPath concluído: {0} pontos gerados", PrismalCurve.ControlPoints.Num());
    return PrismalCurve;
}

float UAURACRONPCGMathLibrary::CalculatePrismalFlowIntensity(
    const FVector& Position,
    const FAURACRONSplineCurve& FlowPath,
    float TeamControlFactor,
    float TimeOfDay)
{
    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "CalculatePrismalFlowIntensity: Pos={0}, TeamControl={1}, Time={2}",
        Position.ToString(), TeamControlFactor, TimeOfDay);

    // Validações robustas
    if (FlowPath.ControlPoints.Num() < 2)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "CalculatePrismalFlowIntensity: FlowPath inválido");
        return 0.0f;
    }

    TeamControlFactor = FMath::Clamp(TeamControlFactor, -1.0f, 1.0f);
    TimeOfDay = FMath::Clamp(TimeOfDay, 0.0f, 1.0f);

    // Encontrar ponto mais próximo no fluxo
    float ClosestT = 0.0f;
    float MinDistance = FLT_MAX;

    for (int32 i = 0; i <= 100; ++i)
    {
        float T = static_cast<float>(i) / 100.0f;
        FVector FlowPoint = EvaluateSplineCurve(FlowPath, T);
        float Distance = FVector::Dist(Position, FlowPoint);

        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            ClosestT = T;
        }
    }

    // Calcular intensidade baseada na proximidade e controle de equipe
    float ProximityFactor = FMath::Exp(-MinDistance / 1000.0f); // Decaimento exponencial
    float ControlIntensity = 0.5f + (TeamControlFactor * 0.5f); // 0.0 a 1.0
    float TimeModifier = 1.0f + 0.3f * FMath::Sin(TimeOfDay * 2.0f * PI); // Variação temporal

    float FinalIntensity = ProximityFactor * ControlIntensity * TimeModifier;

    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "Intensidade calculada: {0} (Proximidade={1}, Controle={2}, Tempo={3})",
        FinalIntensity, ProximityFactor, ControlIntensity, TimeModifier);

    return FMath::Clamp(FinalIntensity, 0.0f, 2.0f);
}

FVector UAURACRONPCGMathLibrary::GetPrismalFlowDirection(
    const FAURACRONSplineCurve& FlowPath,
    const FVector& Position,
    float FlowSpeed)
{
    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "GetPrismalFlowDirection: Pos={0}, Speed={1}", Position.ToString(), FlowSpeed);

    // Validações robustas
    if (FlowPath.ControlPoints.Num() < 2)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "GetPrismalFlowDirection: FlowPath inválido");
        return FVector::ForwardVector;
    }

    if (FlowSpeed <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "FlowSpeed inválida ({0}), usando valor padrão 1.0f", FlowSpeed);
        FlowSpeed = 1.0f;
    }

    // Encontrar ponto mais próximo no fluxo
    float ClosestT = 0.0f;
    float MinDistance = FLT_MAX;

    for (int32 i = 0; i <= 100; ++i)
    {
        float T = static_cast<float>(i) / 100.0f;
        FVector FlowPoint = EvaluateSplineCurve(FlowPath, T);
        float Distance = FVector::Dist(Position, FlowPoint);

        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            ClosestT = T;
        }
    }

    // Obter direção do fluxo no ponto mais próximo
    FVector FlowDirection = GetSplineTangent(FlowPath, ClosestT);
    FlowDirection.Normalize();

    // Aplicar velocidade do fluxo
    FVector FinalDirection = FlowDirection * FlowSpeed;

    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "Direção do fluxo calculada: {0}", FinalDirection.ToString());

    return FinalDirection;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ESPECÍFICAS DOS TRILHOS
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GenerateSolarTrilhoPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float TrilhoWidth,
    int32 NumSegments,
    float TimeOfDay)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateSolarTrilhoPath iniciado: Start={0}, End={1}, Width={2}, Time={3}",
        StartPoint.ToString(), EndPoint.ToString(), TrilhoWidth, TimeOfDay);

    // Validações robustas
    if (NumSegments < 5)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumSegments muito baixo ({0}), ajustando para 5", NumSegments);
        NumSegments = 5;
    }

    if (TrilhoWidth <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "TrilhoWidth inválida ({0}), usando valor padrão 500.0f", TrilhoWidth);
        TrilhoWidth = 500.0f;
    }

    TimeOfDay = FMath::Clamp(TimeOfDay, 0.0f, 1.0f);

    TArray<FVector> TrilhoPoints;
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();

    // Gerar pontos do trilho solar com intensidade baseada na hora do dia
    float SolarIntensity = GetSolarIntensity(TimeOfDay);

    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float T = static_cast<float>(i) / NumSegments;
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);

        // Adicionar ondulação dourada baseada na intensidade solar
        float WaveAmplitude = SolarIntensity * 100.0f;
        float WaveFrequency = 4.0f;
        float WaveOffset = FMath::Sin(T * WaveFrequency * PI + TimeOfDay * 2.0f * PI) * WaveAmplitude;

        FVector CenterPoint = BasePosition + FVector(0.0f, 0.0f, WaveOffset);
        TrilhoPoints.Add(CenterPoint);

        // Adicionar pontos laterais para definir largura
        TrilhoPoints.Add(CenterPoint + Perpendicular * TrilhoWidth * 0.5f);
        TrilhoPoints.Add(CenterPoint - Perpendicular * TrilhoWidth * 0.5f);
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateSolarTrilhoPath concluído: {0} pontos gerados com intensidade solar {1}",
        TrilhoPoints.Num(), SolarIntensity);

    return TrilhoPoints;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateAxisTrilhoPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float TrilhoWidth,
    int32 NumSegments,
    bool bTeamControlled)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateAxisTrilhoPath iniciado: Start={0}, End={1}, Width={2}, Controlled={3}",
        StartPoint.ToString(), EndPoint.ToString(), TrilhoWidth, bTeamControlled);

    // Validações robustas
    if (NumSegments < 5)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumSegments muito baixo ({0}), ajustando para 5", NumSegments);
        NumSegments = 5;
    }

    if (TrilhoWidth <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "TrilhoWidth inválida ({0}), usando valor padrão 400.0f", TrilhoWidth);
        TrilhoWidth = 400.0f;
    }

    TArray<FVector> TrilhoPoints;
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();

    // Gerar pontos do trilho axis com padrões geométricos
    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float T = static_cast<float>(i) / NumSegments;
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);

        // Adicionar padrões geométricos prateados baseados no controle de equipe
        float GeometricOffset = 0.0f;
        if (bTeamControlled)
        {
            // Padrão hexagonal quando controlado
            float HexPattern = FMath::Sin(T * 6.0f * PI) * 50.0f;
            GeometricOffset = HexPattern;
        }
        else
        {
            // Padrão linear neutro quando não controlado
            GeometricOffset = FMath::Sin(T * 2.0f * PI) * 25.0f;
        }

        FVector CenterPoint = BasePosition + FVector(0.0f, 0.0f, GeometricOffset);
        TrilhoPoints.Add(CenterPoint);

        // Adicionar pontos laterais para definir largura
        TrilhoPoints.Add(CenterPoint + Perpendicular * TrilhoWidth * 0.5f);
        TrilhoPoints.Add(CenterPoint - Perpendicular * TrilhoWidth * 0.5f);
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateAxisTrilhoPath concluído: {0} pontos gerados, controle={1}",
        TrilhoPoints.Num(), bTeamControlled);

    return TrilhoPoints;
}

TArray<FVector> UAURACRONPCGMathLibrary::GenerateLunarTrilhoPath(
    const FVector& StartPoint,
    const FVector& EndPoint,
    float TrilhoWidth,
    int32 NumSegments,
    float LunarPhase,
    float TimeOfDay)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateLunarTrilhoPath iniciado: Start={0}, End={1}, Width={2}, Phase={3}, Time={4}",
        StartPoint.ToString(), EndPoint.ToString(), TrilhoWidth, LunarPhase, TimeOfDay);

    // Validações robustas
    if (NumSegments < 5)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumSegments muito baixo ({0}), ajustando para 5", NumSegments);
        NumSegments = 5;
    }

    if (TrilhoWidth <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "TrilhoWidth inválida ({0}), usando valor padrão 300.0f", TrilhoWidth);
        TrilhoWidth = 300.0f;
    }

    LunarPhase = FMath::Clamp(LunarPhase, 0.0f, 1.0f);
    TimeOfDay = FMath::Clamp(TimeOfDay, 0.0f, 1.0f);

    TArray<FVector> TrilhoPoints;
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();

    // Calcular intensidade lunar baseada na fase e hora do dia
    float LunarIntensity = GetLunarPhaseIntensity(TimeOfDay, 28.0f);
    float PhaseModifier = 1.0f - FMath::Abs(LunarPhase - 0.5f) * 2.0f; // Máximo na lua cheia (0.5)
    float FinalIntensity = LunarIntensity * PhaseModifier;

    // Gerar pontos do trilho lunar com efeitos etéreos
    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float T = static_cast<float>(i) / NumSegments;
        FVector BasePosition = FMath::Lerp(StartPoint, EndPoint, T);

        // Adicionar ondulação etérea baseada na fase lunar
        float EtherealAmplitude = FinalIntensity * 150.0f;
        float EtherealFrequency = 3.0f;
        float PhaseOffset = LunarPhase * 2.0f * PI;

        float EtherealOffset = FMath::Sin(T * EtherealFrequency * PI + PhaseOffset) * EtherealAmplitude;
        float SecondaryWave = FMath::Cos(T * EtherealFrequency * 2.0f * PI + PhaseOffset) * EtherealAmplitude * 0.3f;

        FVector CenterPoint = BasePosition + FVector(0.0f, 0.0f, EtherealOffset + SecondaryWave);

        // Trilhos lunares só são visíveis à noite (intensidade > 0.1)
        if (FinalIntensity > 0.1f)
        {
            TrilhoPoints.Add(CenterPoint);

            // Adicionar pontos laterais com largura variável baseada na intensidade
            float VariableWidth = TrilhoWidth * FMath::Clamp(FinalIntensity, 0.3f, 1.0f);
            TrilhoPoints.Add(CenterPoint + Perpendicular * VariableWidth * 0.5f);
            TrilhoPoints.Add(CenterPoint - Perpendicular * VariableWidth * 0.5f);
        }
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateLunarTrilhoPath concluído: {0} pontos gerados, intensidade={1}",
        TrilhoPoints.Num(), FinalIntensity);

    return TrilhoPoints;
}

TArray<FVector> UAURACRONPCGMathLibrary::CalculateTrilhoIntersections(
    const TArray<FVector>& SolarTrilho,
    const TArray<FVector>& AxisTrilho,
    const TArray<FVector>& LunarTrilho,
    float IntersectionRadius)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "CalculateTrilhoIntersections iniciado: Solar={0}, Axis={1}, Lunar={2}, Radius={3}",
        SolarTrilho.Num(), AxisTrilho.Num(), LunarTrilho.Num(), IntersectionRadius);

    // Validações robustas
    if (IntersectionRadius <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "IntersectionRadius inválido ({0}), usando valor padrão 200.0f", IntersectionRadius);
        IntersectionRadius = 200.0f;
    }

    TArray<FVector> Intersections;

    // Encontrar intersecções entre Solar e Axis
    for (const FVector& SolarPoint : SolarTrilho)
    {
        for (const FVector& AxisPoint : AxisTrilho)
        {
            float Distance = FVector::Dist(SolarPoint, AxisPoint);
            if (Distance <= IntersectionRadius)
            {
                FVector IntersectionPoint = (SolarPoint + AxisPoint) * 0.5f;
                Intersections.Add(IntersectionPoint);
            }
        }
    }

    // Encontrar intersecções entre Solar e Lunar
    for (const FVector& SolarPoint : SolarTrilho)
    {
        for (const FVector& LunarPoint : LunarTrilho)
        {
            float Distance = FVector::Dist(SolarPoint, LunarPoint);
            if (Distance <= IntersectionRadius)
            {
                FVector IntersectionPoint = (SolarPoint + LunarPoint) * 0.5f;
                Intersections.Add(IntersectionPoint);
            }
        }
    }

    // Encontrar intersecções entre Axis e Lunar
    for (const FVector& AxisPoint : AxisTrilho)
    {
        for (const FVector& LunarPoint : LunarTrilho)
        {
            float Distance = FVector::Dist(AxisPoint, LunarPoint);
            if (Distance <= IntersectionRadius)
            {
                FVector IntersectionPoint = (AxisPoint + LunarPoint) * 0.5f;
                Intersections.Add(IntersectionPoint);
            }
        }
    }

    UE_LOGFMT(LogAURACRONPCGMath, Log, "CalculateTrilhoIntersections concluído: {0} intersecções encontradas", Intersections.Num());

    return Intersections;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ESPECÍFICAS DA ILHA CENTRAL AURACRON
// ========================================

TArray<FVector> UAURACRONPCGMathLibrary::GenerateAuracronIslandLayout(
    const FVector& IslandCenter,
    float IslandRadius,
    int32 NumSectors)
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateAuracronIslandLayout iniciado: Center={0}, Radius={1}, Sectors={2}",
        IslandCenter.ToString(), IslandRadius, NumSectors);

    // Validações robustas
    if (IslandRadius <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "IslandRadius inválido ({0}), usando valor padrão 1500.0f", IslandRadius);
        IslandRadius = 1500.0f;
    }

    if (NumSectors != 4)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "NumSectors deve ser 4 (Nexus, Santuário, Arsenal, Caos), ajustando de {0} para 4", NumSectors);
        NumSectors = 4;
    }

    TArray<FVector> IslandLayout;

    // Gerar layout da ilha central com 4 setores específicos da documentação
    // Setor Nexus (Norte) - Geradores de recursos e manipulação do Fluxo
    FVector NexusCenter = IslandCenter + FVector(0.0f, IslandRadius * 0.5f, 0.0f);
    IslandLayout.Add(NexusCenter);

    // Setor Santuário (Leste) - Fontes de cura e amplificadores de visão
    FVector SanctuarioCenter = IslandCenter + FVector(IslandRadius * 0.5f, 0.0f, 0.0f);
    IslandLayout.Add(SanctuarioCenter);

    // Setor Arsenal (Sul) - Upgrades de armas e potencializadores de habilidades
    FVector ArsenalCenter = IslandCenter + FVector(0.0f, -IslandRadius * 0.5f, 0.0f);
    IslandLayout.Add(ArsenalCenter);

    // Setor Caos (Oeste) - Perigos ambientais com recompensas de alto risco
    FVector CaosCenter = IslandCenter + FVector(-IslandRadius * 0.5f, 0.0f, 0.0f);
    IslandLayout.Add(CaosCenter);

    // Adicionar pontos de transição entre setores
    for (int32 i = 0; i < 4; ++i)
    {
        float Angle = (i * 90.0f + 45.0f) * PI / 180.0f; // 45°, 135°, 225°, 315°
        FVector TransitionPoint = IslandCenter + FVector(
            FMath::Cos(Angle) * IslandRadius * 0.7f,
            FMath::Sin(Angle) * IslandRadius * 0.7f,
            0.0f
        );
        IslandLayout.Add(TransitionPoint);
    }

    // Adicionar centro da ilha (torre de controle central)
    IslandLayout.Add(IslandCenter);

    UE_LOGFMT(LogAURACRONPCGMath, Log, "GenerateAuracronIslandLayout concluído: {0} pontos gerados", IslandLayout.Num());

    return IslandLayout;
}

float UAURACRONPCGMathLibrary::CalculateSectorControl(
    const FVector& SectorCenter,
    float SectorRadius,
    const TArray<FVector>& TeamAPositions,
    const TArray<FVector>& TeamBPositions)
{
    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "CalculateSectorControl: Center={0}, Radius={1}, TeamA={2}, TeamB={3}",
        SectorCenter.ToString(), SectorRadius, TeamAPositions.Num(), TeamBPositions.Num());

    // Validações robustas
    if (SectorRadius <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "SectorRadius inválido ({0}), usando valor padrão 500.0f", SectorRadius);
        SectorRadius = 500.0f;
    }

    int32 TeamACount = 0;
    int32 TeamBCount = 0;

    // Contar jogadores da Team A dentro do setor
    for (const FVector& Position : TeamAPositions)
    {
        float Distance = FVector::Dist2D(Position, SectorCenter);
        if (Distance <= SectorRadius)
        {
            TeamACount++;
        }
    }

    // Contar jogadores da Team B dentro do setor
    for (const FVector& Position : TeamBPositions)
    {
        float Distance = FVector::Dist2D(Position, SectorCenter);
        if (Distance <= SectorRadius)
        {
            TeamBCount++;
        }
    }

    // Calcular controle do setor (-1.0 = Team B, 0.0 = Neutro, 1.0 = Team A)
    float TotalPlayers = static_cast<float>(TeamACount + TeamBCount);
    float ControlFactor = 0.0f;

    if (TotalPlayers > 0.0f)
    {
        float TeamAFactor = static_cast<float>(TeamACount) / TotalPlayers;
        float TeamBFactor = static_cast<float>(TeamBCount) / TotalPlayers;
        ControlFactor = TeamAFactor - TeamBFactor;
    }

    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "Controle do setor calculado: {0} (TeamA={1}, TeamB={2})",
        ControlFactor, TeamACount, TeamBCount);

    return FMath::Clamp(ControlFactor, -1.0f, 1.0f);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE OTIMIZAÇÃO POR HARDWARE
// ========================================

int32 UAURACRONPCGMathLibrary::CalculateHardwareTier()
{
    UE_LOGFMT(LogAURACRONPCGMath, Log, "CalculateHardwareTier iniciado");

    // Detectar nível de hardware baseado nas especificações da documentação
    int32 HardwareTier = 1; // Entry por padrão

    // Obter informações de hardware usando APIs modernas do UE 5.6
    const FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    uint64 TotalPhysicalGB = MemoryStats.TotalPhysical / (1024 * 1024 * 1024);

    // Classificar baseado na documentação:
    // Entry Level: 2-3GB RAM
    // Mid-Range: 3-4GB RAM
    // High-End: 4GB+ RAM

    if (TotalPhysicalGB >= 4)
    {
        HardwareTier = 3; // High-End
        UE_LOGFMT(LogAURACRONPCGMath, Log, "Hardware detectado: High-End ({0}GB RAM)", TotalPhysicalGB);
    }
    else if (TotalPhysicalGB >= 3)
    {
        HardwareTier = 2; // Mid-Range
        UE_LOGFMT(LogAURACRONPCGMath, Log, "Hardware detectado: Mid-Range ({0}GB RAM)", TotalPhysicalGB);
    }
    else
    {
        HardwareTier = 1; // Entry Level
        UE_LOGFMT(LogAURACRONPCGMath, Log, "Hardware detectado: Entry Level ({0}GB RAM)", TotalPhysicalGB);
    }

    return HardwareTier;
}

int32 UAURACRONPCGMathLibrary::GetOptimalParticleCount(
    int32 HardwareTier,
    const FString& EffectType)
{
    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "GetOptimalParticleCount: Tier={0}, Type={1}", HardwareTier, EffectType);

    // Validações robustas
    HardwareTier = FMath::Clamp(HardwareTier, 1, 3);

    int32 ParticleCount = 100; // Padrão

    // Orçamentos de partículas baseados na documentação
    if (EffectType == TEXT("Trilhos"))
    {
        switch (HardwareTier)
        {
            case 1: // Entry Level
                ParticleCount = 100; // apenas trilho ativo
                break;
            case 2: // Mid-Range
                ParticleCount = 250; // 2 trilhos máximo
                break;
            case 3: // High-End
                ParticleCount = 500; // todos os trilhos
                break;
        }
    }
    else if (EffectType == TEXT("FluxoPrismal"))
    {
        switch (HardwareTier)
        {
            case 1: // Entry Level
                ParticleCount = 300;
                break;
            case 2: // Mid-Range
                ParticleCount = 800;
                break;
            case 3: // High-End
                ParticleCount = 2000;
                break;
        }
    }
    else if (EffectType == TEXT("Ambiental"))
    {
        switch (HardwareTier)
        {
            case 1: // Entry Level
                ParticleCount = 200;
                break;
            case 2: // Mid-Range
                ParticleCount = 500;
                break;
            case 3: // High-End
                ParticleCount = 1000;
                break;
        }
    }
    else if (EffectType == TEXT("Combate"))
    {
        switch (HardwareTier)
        {
            case 1: // Entry Level
                ParticleCount = 500;
                break;
            case 2: // Mid-Range
                ParticleCount = 1500;
                break;
            case 3: // High-End
                ParticleCount = 3000;
                break;
        }
    }

    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "Contagem ótima de partículas: {0} para {1} (Tier {2})",
        ParticleCount, EffectType, HardwareTier);

    return ParticleCount;
}

bool UAURACRONPCGMathLibrary::ShouldUseAdvancedEffects(
    int32 HardwareTier,
    float CurrentFrameRate,
    float TargetFrameRate)
{
    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "ShouldUseAdvancedEffects: Tier={0}, FPS={1}, Target={2}",
        HardwareTier, CurrentFrameRate, TargetFrameRate);

    // Validações robustas
    HardwareTier = FMath::Clamp(HardwareTier, 1, 3);

    if (TargetFrameRate <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGMath, Warning, "TargetFrameRate inválido ({0}), usando 30.0f", TargetFrameRate);
        TargetFrameRate = 30.0f;
    }

    // Determinar se deve usar efeitos avançados baseado no hardware e performance
    bool bUseAdvancedEffects = false;

    // Verificar se o framerate está acima do target
    float PerformanceRatio = CurrentFrameRate / TargetFrameRate;

    switch (HardwareTier)
    {
        case 1: // Entry Level
            // Apenas efeitos básicos, mesmo com bom framerate
            bUseAdvancedEffects = false;
            break;

        case 2: // Mid-Range
            // Efeitos avançados apenas se framerate estiver 20% acima do target
            bUseAdvancedEffects = (PerformanceRatio >= 1.2f);
            break;

        case 3: // High-End
            // Efeitos avançados se framerate estiver no target ou acima
            bUseAdvancedEffects = (PerformanceRatio >= 1.0f);
            break;
    }

    UE_LOGFMT(LogAURACRONPCGMath, VeryVerbose, "Usar efeitos avançados: {0} (Ratio={1})",
        bUseAdvancedEffects, PerformanceRatio);

    return bUseAdvancedEffects;
}


